<?php
// Simple test to verify encrypted file execution
echo "Testing browser-compatible encrypted file execution...\n";

// Test one of the smaller encrypted files
$test_file = 'browser_compatible_laravel_files_20250626_111714/encrypted_laravel_run_artisan.php';

if (file_exists($test_file)) {
    echo "Found test file: $test_file\n";
    echo "File size: " . filesize($test_file) . " bytes\n";
    
    // Capture output from the encrypted file
    ob_start();
    try {
        include $test_file;
        $output = ob_get_contents();
        ob_end_clean();
        
        if (!empty($output)) {
            echo "✅ Encrypted file executed successfully!\n";
            echo "Output preview: " . substr($output, 0, 200) . "...\n";
        } else {
            echo "⚠️ Encrypted file executed but produced no output (this may be normal)\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ Error executing encrypted file: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Test file not found: $test_file\n";
}

echo "\nTest completed.\n";
?>
