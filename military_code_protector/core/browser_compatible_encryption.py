#!/usr/bin/env python3
"""
Browser-Compatible Encryption Engine
Creates encrypted PHP files that execute reliably in web browsers.
"""

import os
import base64
import random
import string
import hashlib
from typing import Dict, Any, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BrowserCompatibleEncryption:
    """Simple, reliable encryption for browser execution."""
    
    def __init__(self):
        self.encryption_key = self._generate_key()
    
    def _generate_key(self) -> str:
        """Generate a random encryption key."""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=32))
    
    def _xor_encrypt(self, data: str, key: str) -> bytes:
        """Simple XOR encryption that handles UTF-8."""
        # Convert string to UTF-8 bytes
        data_bytes = data.encode('utf-8')
        key_bytes = key.encode('utf-8')

        result = []
        for i, byte_val in enumerate(data_bytes):
            key_byte = key_bytes[i % len(key_bytes)]
            encrypted_byte = byte_val ^ key_byte
            result.append(encrypted_byte)

        return bytes(result)
    
    def _create_decoder_template(self, encrypted_data: str, key: str) -> str:
        """Create the decoder PHP template."""
        
        # Generate random variable names for obfuscation
        var_encrypted = '_' + ''.join(random.choices(string.ascii_letters, k=8))
        var_key = '_' + ''.join(random.choices(string.ascii_letters, k=8))
        var_decoded = '_' + ''.join(random.choices(string.ascii_letters, k=8))
        var_result = '_' + ''.join(random.choices(string.ascii_letters, k=8))
        var_i = '_' + ''.join(random.choices(string.ascii_letters, k=6))
        
        template = f'''<?php
// Production-safe anti-debugging measures
if (isset($_GET['XDEBUG_SESSION']) || isset($_POST['XDEBUG_SESSION']) || isset($_COOKIE['XDEBUG_SESSION'])) {{
    exit('Debug session detected');
}}

// Check for common debugging tools
if (function_exists('xdebug_is_enabled') && xdebug_is_enabled()) {{
    exit('Debugging tools detected');
}}

// Browser-compatible encrypted content
${var_encrypted} = "{encrypted_data}";
${var_key} = "{key}";

// Simple and reliable decoder
${var_decoded} = base64_decode(${var_encrypted});
${var_result} = '';

// XOR decryption with UTF-8 support
${var_key}_bytes = ${var_key};
for (${var_i} = 0; ${var_i} < strlen(${var_decoded}); ${var_i}++) {{
    $key_byte = ord(${var_key}_bytes[${var_i} % strlen(${var_key}_bytes)]);
    $data_byte = ord(${var_decoded}[${var_i}]);
    ${var_result} .= chr($data_byte ^ $key_byte);
}}

// Execute the decrypted content
if (!empty(${var_result})) {{
    eval('?>' . ${var_result});
}} else {{
    // Fallback for compatibility
    header('HTTP/1.0 404 Not Found');
    exit();
}}
?>'''
        
        return template
    
    def encrypt_file(self, input_path: str, output_path: str = None) -> Dict[str, Any]:
        """
        Encrypt a PHP file with browser-compatible encryption.
        
        Args:
            input_path (str): Path to the input file
            output_path (str): Path for the encrypted output file
            
        Returns:
            dict: Encryption results and statistics
        """
        try:
            logger.info(f"Starting browser-compatible encryption of: {input_path}")
            
            # Read the input file
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"Input file not found: {input_path}")
            
            with open(input_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Remove PHP opening tag if present
            if original_content.startswith('<?php'):
                original_content = original_content[5:].lstrip()
            
            # Generate encryption key
            encryption_key = self._generate_key()
            
            # XOR encrypt the content (returns bytes)
            encrypted_bytes = self._xor_encrypt(original_content, encryption_key)

            # Base64 encode the encrypted bytes
            base64_encrypted = base64.b64encode(encrypted_bytes).decode('ascii')
            
            # Create the decoder
            decoder_code = self._create_decoder_template(base64_encrypted, encryption_key)
            
            # Determine output path
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = f"encrypted_{base_name}.php"
            
            # Write the encrypted file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(decoder_code)
            
            # Calculate statistics
            original_size = len(original_content)
            encrypted_size = len(decoder_code)
            size_increase = ((encrypted_size - original_size) / original_size) * 100
            
            result = {
                'status': 'success',
                'input_path': input_path,
                'output_path': output_path,
                'encryption_method': 'browser_compatible',
                'stats': {
                    'original_size': original_size,
                    'encrypted_size': encrypted_size,
                    'size_increase_percent': round(size_increase, 1),
                    'encryption_key_length': len(encryption_key)
                }
            }
            
            logger.info(f"Browser-compatible encryption completed: {output_path}")
            logger.info(f"Size increase: {size_increase:.1f}%")
            
            return result
            
        except Exception as e:
            logger.error(f"Browser-compatible encryption failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'input_path': input_path
            }
    
    def batch_encrypt_files(self, file_paths: list, output_dir: str = None) -> Dict[str, Any]:
        """
        Encrypt multiple files with browser-compatible encryption.
        
        Args:
            file_paths (list): List of file paths to encrypt
            output_dir (str): Output directory for encrypted files
            
        Returns:
            dict: Batch encryption results
        """
        if output_dir is None:
            output_dir = f"browser_compatible_encrypted_{self._get_timestamp()}"
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        results = {
            'status': 'success',
            'output_dir': output_dir,
            'total_files': len(file_paths),
            'successful': 0,
            'failed': 0,
            'results': []
        }
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                logger.warning(f"File not found: {file_path}")
                results['failed'] += 1
                continue
            
            # Generate output path
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_path = os.path.join(output_dir, f"encrypted_{base_name}.php")
            
            # Encrypt the file
            result = self.encrypt_file(file_path, output_path)
            results['results'].append(result)
            
            if result['status'] == 'success':
                results['successful'] += 1
            else:
                results['failed'] += 1
        
        logger.info(f"Batch encryption completed: {results['successful']}/{results['total_files']} successful")
        
        return results
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for directory naming."""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")

def main():
    """Test the browser-compatible encryption."""
    encryptor = BrowserCompatibleEncryption()
    
    # Test with a simple PHP file
    test_content = '''<?php
echo "Hello, World!";
echo "Current time: " . date('Y-m-d H:i:s');
?>'''
    
    # Create test file
    with open('test_simple.php', 'w') as f:
        f.write(test_content)
    
    # Encrypt it
    result = encryptor.encrypt_file('test_simple.php', 'test_encrypted.php')
    print(f"Test encryption result: {result}")
    
    # Clean up
    if os.path.exists('test_simple.php'):
        os.remove('test_simple.php')

if __name__ == '__main__':
    main()
