#!/usr/bin/env python3
"""
Re-encrypt Laravel Files with Browser-Compatible Encryption
Fixes the issue where encrypted files show source code instead of executing.
"""

import os
import sys
import json
import subprocess
from datetime import datetime
from pathlib import Path

# Add the military_code_protector to the path
sys.path.append('military_code_protector')
sys.path.append('military_code_protector/core')

from military_code_protector.core.browser_compatible_encryption import BrowserCompatibleEncryption

class LaravelBrowserCompatibleEncryptor:
    """Re-encrypt Laravel files with browser-compatible encryption."""
    
    def __init__(self):
        self.encryptor = BrowserCompatibleEncryption()
        self.target_files = [
            'laravel_db_migrate.php',
            'laravel_db_restore.php', 
            'laravel_developer_toolkit.php',
            'laravel_npm_build.php',
            'laravel_permissions_fixer.php',
            'laravel_prod_error-fixer.php',
            'laravel_run_artisan.php',
            'laravel_symlink_creator.php'
        ]
    
    def find_target_files(self) -> list:
        """Find all target Laravel files in the current directory."""
        found_files = []
        
        for file_name in self.target_files:
            if os.path.exists(file_name):
                found_files.append(file_name)
                print(f"✓ Found: {file_name}")
            else:
                print(f"✗ Missing: {file_name}")
        
        return found_files
    
    def encrypt_all_files(self) -> dict:
        """Encrypt all target Laravel files with browser-compatible encryption."""
        print("🔄 Starting Browser-Compatible Re-encryption of Laravel Files")
        print("=" * 60)
        
        # Find target files
        target_files = self.find_target_files()
        
        if not target_files:
            print("❌ No target files found!")
            return {'status': 'error', 'message': 'No files found'}
        
        print(f"\n📋 Found {len(target_files)} files to encrypt")
        
        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"browser_compatible_laravel_files_{timestamp}"
        
        # Encrypt files
        start_time = datetime.now()
        results = self.encryptor.batch_encrypt_files(target_files, output_dir)
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        results['processing_time'] = processing_time
        
        # Display results
        print(f"\n📊 Encryption Results:")
        print(f"   Output Directory: {output_dir}")
        print(f"   Total Files: {results['total_files']}")
        print(f"   Successful: {results['successful']}")
        print(f"   Failed: {results['failed']}")
        print(f"   Processing Time: {processing_time:.2f}s")
        
        # Show individual results
        print(f"\n📝 Individual File Results:")
        for result in results['results']:
            if result['status'] == 'success':
                stats = result['stats']
                file_name = os.path.basename(result['input_path'])
                print(f"   ✅ {file_name}")
                print(f"      Size: {stats['original_size']} → {stats['encrypted_size']} bytes")
                print(f"      Increase: +{stats['size_increase_percent']}%")
                print(f"      Method: {result['encryption_method']}")
            else:
                file_name = os.path.basename(result['input_path'])
                print(f"   ❌ {file_name}: {result['error']}")
        
        # Save detailed report
        report_file = os.path.join(output_dir, 'browser_compatible_encryption_report.json')
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📄 Detailed report saved: {report_file}")
        
        return results
    
    def test_encrypted_files(self, output_dir: str) -> dict:
        """Test the encrypted files for PHP syntax validity."""
        print(f"\n🧪 Testing Encrypted Files in: {output_dir}")
        print("-" * 40)
        
        if not os.path.exists(output_dir):
            print(f"❌ Output directory not found: {output_dir}")
            return {'status': 'error', 'message': 'Directory not found'}
        
        encrypted_files = [f for f in os.listdir(output_dir) if f.endswith('.php')]
        
        test_results = {
            'total': len(encrypted_files),
            'passed': 0,
            'failed': 0,
            'details': []
        }
        
        for file_name in sorted(encrypted_files):
            file_path = os.path.join(output_dir, file_name)
            
            print(f"🔍 Testing: {file_name}")
            
            # Test PHP syntax
            try:
                result = subprocess.run(['php', '-l', file_path], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"   ✅ Syntax: OK")
                    test_results['passed'] += 1
                    status = 'passed'
                    error = None
                else:
                    print(f"   ❌ Syntax: FAILED")
                    print(f"   Error: {result.stderr}")
                    test_results['failed'] += 1
                    status = 'failed'
                    error = result.stderr
                
                # Get file size
                file_size = os.path.getsize(file_path)
                
                test_results['details'].append({
                    'file': file_name,
                    'status': status,
                    'size': file_size,
                    'error': error
                })
                
            except Exception as e:
                print(f"   ❌ Test failed: {str(e)}")
                test_results['failed'] += 1
                test_results['details'].append({
                    'file': file_name,
                    'status': 'failed',
                    'error': str(e)
                })
        
        # Summary
        print(f"\n📊 Test Summary:")
        print(f"   Total: {test_results['total']}")
        print(f"   Passed: {test_results['passed']}")
        print(f"   Failed: {test_results['failed']}")
        print(f"   Success Rate: {(test_results['passed']/test_results['total']*100):.1f}%")
        
        return test_results

def main():
    """Main execution function."""
    print("🔒 Laravel Browser-Compatible Re-encryption Tool")
    print("Fixes encrypted files that show source code instead of executing")
    print("=" * 70)
    
    encryptor = LaravelBrowserCompatibleEncryptor()
    
    # Encrypt all files
    results = encryptor.encrypt_all_files()
    
    if results['status'] == 'error':
        print(f"❌ Encryption failed: {results['message']}")
        return
    
    # Test the encrypted files
    if results['successful'] > 0:
        test_results = encryptor.test_encrypted_files(results['output_dir'])
        
        # Save test results
        test_report_file = os.path.join(results['output_dir'], 'syntax_test_report.json')
        with open(test_report_file, 'w') as f:
            json.dump(test_results, f, indent=2)
        
        print(f"\n📄 Test report saved: {test_report_file}")
        
        if test_results['failed'] == 0:
            print(f"\n🎉 SUCCESS! All {test_results['total']} files encrypted and tested successfully!")
            print(f"📂 Browser-compatible encrypted files are in: {results['output_dir']}")
            print("\n💡 These files should now execute properly in web browsers instead of showing source code.")
        else:
            print(f"\n⚠️  {test_results['failed']} files have syntax issues that need attention.")
    
    print("\n" + "=" * 70)

if __name__ == '__main__':
    main()
